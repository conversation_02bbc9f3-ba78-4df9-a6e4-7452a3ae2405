<?php
/**
 * 订单查询API接口
 * 提供JSON格式的订单查询服务
 */

// 设置响应头
header('Content-Type: application/json; charset=UTF-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// 引入数据库配置
include('../confing/mysqlset.php');

// 创建数据库连接
$mysqli = new mysqli($host, $user, $pwd, $dbname, $port);
if ($mysqli->connect_error) {
    http_response_code(500);
    echo json_encode([
        'code' => -1,
        'msg' => '数据库连接失败',
        'data' => null
    ]);
    exit;
}
$mysqli->set_charset("utf8");

// 获取请求参数
$username = '';
$usernames = [];
$is_batch = false;
$method = $_SERVER['REQUEST_METHOD'];

if ($method === 'POST') {
    $input = json_decode(file_get_contents('php://input'), true);

    // 检查是否为批量查询
    if (isset($input['usernames']) && is_array($input['usernames'])) {
        $usernames = array_filter(array_map('trim', $input['usernames']));
        $is_batch = true;
    } elseif (isset($input['username'])) {
        $username = trim($input['username']);
        $usernames = [$username];
    }

    // 如果JSON解析失败，尝试从POST参数获取
    if (empty($usernames)) {
        if (isset($_POST['usernames']) && is_array($_POST['usernames'])) {
            $usernames = array_filter(array_map('trim', $_POST['usernames']));
            $is_batch = true;
        } elseif (isset($_POST['username'])) {
            $username = trim($_POST['username']);
            $usernames = [$username];
        }
    }
} else if ($method === 'GET') {
    if (isset($_GET['usernames'])) {
        // GET请求的批量查询，使用逗号分隔
        $usernames = array_filter(array_map('trim', explode(',', $_GET['usernames'])));
        $is_batch = true;
    } elseif (isset($_GET['username'])) {
        $username = trim($_GET['username']);
        $usernames = [$username];
    }
}

// 验证参数
if (empty($usernames)) {
    echo json_encode([
        'code' => -1,
        'msg' => '请提供要查询的账号',
        'data' => null
    ]);
    exit;
}

// 限制批量查询数量
if (count($usernames) > 100) {
    echo json_encode([
        'code' => -1,
        'msg' => '批量查询最多支持100个账号，您提供了' . count($usernames) . '个账号',
        'data' => null
    ]);
    exit;
}

try {
    // 构建查询SQL
    $placeholders = str_repeat('?,', count($usernames) - 1) . '?';
    $sql = "SELECT oid, ptname, school, name, user, kcname, status, process, remarks, addtime,
                   courseStartTime, courseEndTime, examStartTime, examEndTime, fees
            FROM qingka_wangke_order
            WHERE user IN ($placeholders)
            ORDER BY user, oid DESC
            LIMIT 1000";

    $stmt = $mysqli->prepare($sql);
    if (!$stmt) {
        throw new Exception('SQL准备失败: ' . $mysqli->error);
    }

    $types = str_repeat('s', count($usernames));
    $stmt->bind_param($types, ...$usernames);
    $stmt->execute();
    $result = $stmt->get_result();

    $orders = [];
    $grouped_orders = [];

    while ($row = $result->fetch_assoc()) {
        // 处理进度数据
        $process = $row['process'];
        if (!empty($process) && !strpos($process, '%')) {
            $process .= '%';
        }

        // 格式化订单数据
        $order_data = [
            'id' => (int)$row['oid'],
            'platform' => $row['ptname'],
            'school' => $row['school'] ?: '',
            'name' => $row['name'] ?: '',
            'username' => $row['user'],
            'course_name' => $row['kcname'],
            'status' => $row['status'],
            'progress' => $process,
            'remarks' => $row['remarks'] ?: '',
            'order_time' => $row['addtime'],
            'course_start_time' => $row['courseStartTime'] ?: '',
            'course_end_time' => $row['courseEndTime'] ?: '',
            'exam_start_time' => $row['examStartTime'] ?: '',
            'exam_end_time' => $row['examEndTime'] ?: '',
            'fees' => $row['fees'] ? (float)$row['fees'] : 0
        ];

        $orders[] = $order_data;
        $grouped_orders[$row['user']][] = $order_data;
    }

    $stmt->close();

    // 返回结果
    if (empty($orders)) {
        $msg = $is_batch ? '未找到任何账号的订单信息' : '未找到该账号的订单信息';
        echo json_encode([
            'code' => 0,
            'msg' => $msg,
            'data' => []
        ]);
    } else {
        $found_users = array_keys($grouped_orders);
        $not_found_users = array_diff($usernames, $found_users);

        $response = [
            'code' => 1,
            'msg' => '查询成功',
            'data' => $orders,
            'total' => count($orders),
            'is_batch' => $is_batch
        ];

        if ($is_batch) {
            $response['grouped_data'] = $grouped_orders;
            $response['found_users'] = $found_users;
            $response['not_found_users'] = $not_found_users;
            $response['user_count'] = count($found_users);
        }

        echo json_encode($response);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'code' => -1,
        'msg' => '查询失败: ' . $e->getMessage(),
        'data' => null
    ]);
} finally {
    $mysqli->close();
}
?>
