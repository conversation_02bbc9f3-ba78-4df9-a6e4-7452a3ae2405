# 订单查询API对接文档

## 📋 概述

本文档详细说明订单查询API的对接方法，包括接口规范、请求格式、响应格式、错误处理等内容。

## 🌐 接口信息

### 基础信息
- **接口地址**: `http://117.72.158.75/api/check_order.php`
- **请求方式**: `GET` / `POST`
- **数据格式**: `JSON` / `Form Data`
- **字符编码**: `UTF-8`
- **超时时间**: 建议30秒

### 接口特性
- ✅ 支持单个账号查询
- ✅ 支持批量账号查询（最多100个）
- ✅ 支持跨域访问（CORS）
- ✅ 防SQL注入保护
- ✅ 标准HTTP状态码

## 📝 请求规范

### 1. 单个账号查询

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| username | string | 是 | 要查询的学习账号 | "zhangsan" |

#### 请求示例

**POST请求（JSON格式）**
```bash
curl -X POST "https://您的域名/api/check_order.php" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "zhangsan"
  }'
```

**POST请求（表单格式）**
```bash
curl -X POST "https://您的域名/api/check_order.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=zhangsan"
```

**GET请求**
```bash
curl "https://您的域名/api/check_order.php?username=zhangsan"
```

### 2. 批量账号查询

#### 请求参数
| 参数名 | 类型 | 必填 | 说明 | 示例 |
|--------|------|------|------|------|
| usernames | array | 是 | 要查询的学习账号数组，最多100个 | ["zhangsan", "lisi"] |

#### 请求示例

**POST请求（JSON格式）**
```bash
curl -X POST "https://您的域名/api/check_order.php" \
  -H "Content-Type: application/json" \
  -d '{
    "usernames": ["zhangsan", "lisi", "wangwu"]
  }'
```

**POST请求（表单格式）**
```bash
curl -X POST "https://您的域名/api/check_order.php" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "usernames[]=zhangsan&usernames[]=lisi&usernames[]=wangwu"
```

**GET请求（逗号分隔）**
```bash
curl "https://您的域名/api/check_order.php?usernames=zhangsan,lisi,wangwu"
```

## 📤 响应规范

### 响应格式
所有响应均为JSON格式，包含以下基础字段：

| 字段名 | 类型 | 说明 |
|--------|------|------|
| code | int | 状态码：1=成功，0=无数据，-1=失败 |
| msg | string | 响应消息 |
| data | array | 订单数据数组 |

### 订单数据字段
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | int | 订单ID | 12345 |
| platform | string | 平台名称 | "8090教育" |
| school | string | 学校名称 | "某某大学" |
| name | string | 学员姓名 | "张三" |
| username | string | 学习账号 | "zhangsan" |
| course_name | string | 课程名称 | "大学英语" |
| status | string | 订单状态 | "进行中" |
| progress | string | 完成进度 | "75%" |
| remarks | string | 备注信息 | "正常进行中" |
| order_time | string | 下单时间 | "2025-01-01 10:00:00" |
| course_start_time | string | 课程开始时间 | "2025-01-01 08:00:00" |
| course_end_time | string | 课程结束时间 | "2025-06-30 18:00:00" |
| exam_start_time | string | 考试开始时间 | "2025-06-15 09:00:00" |
| exam_end_time | string | 考试结束时间 | "2025-06-15 11:00:00" |
| fees | float | 订单费用 | 50.00 |

## 📋 响应示例

### 1. 单个账号查询成功
```json
{
  "code": 1,
  "msg": "查询成功",
  "total": 2,
  "is_batch": false,
  "data": [
    {
      "id": 12345,
      "platform": "8090教育",
      "school": "某某大学",
      "name": "张三",
      "username": "zhangsan",
      "course_name": "大学英语",
      "status": "进行中",
      "progress": "75%",
      "remarks": "正常进行中",
      "order_time": "2025-01-01 10:00:00",
      "course_start_time": "2025-01-01 08:00:00",
      "course_end_time": "2025-06-30 18:00:00",
      "exam_start_time": "2025-06-15 09:00:00",
      "exam_end_time": "2025-06-15 11:00:00",
      "fees": 50.00
    },
    {
      "id": 12346,
      "platform": "易教育",
      "school": "某某大学",
      "name": "张三",
      "username": "zhangsan",
      "course_name": "高等数学",
      "status": "已完成",
      "progress": "100%",
      "remarks": "已完成所有任务",
      "order_time": "2024-12-01 14:30:00",
      "course_start_time": "2024-12-01 08:00:00",
      "course_end_time": "2024-12-31 18:00:00",
      "exam_start_time": "",
      "exam_end_time": "",
      "fees": 80.00
    }
  ]
}
```

### 2. 批量账号查询成功
```json
{
  "code": 1,
  "msg": "查询成功",
  "total": 3,
  "is_batch": true,
  "user_count": 2,
  "found_users": ["zhangsan", "lisi"],
  "not_found_users": ["wangwu"],
  "data": [
    {
      "id": 12345,
      "platform": "8090教育",
      "username": "zhangsan",
      "course_name": "大学英语",
      "status": "进行中",
      "progress": "75%",
      "order_time": "2025-01-01 10:00:00",
      "fees": 50.00
    },
    {
      "id": 12347,
      "platform": "易教育",
      "username": "lisi",
      "course_name": "线性代数",
      "status": "已完成",
      "progress": "100%",
      "order_time": "2024-11-15 09:20:00",
      "fees": 60.00
    }
  ],
  "grouped_data": {
    "zhangsan": [
      {
        "id": 12345,
        "platform": "8090教育",
        "username": "zhangsan",
        "course_name": "大学英语",
        "status": "进行中",
        "progress": "75%",
        "order_time": "2025-01-01 10:00:00",
        "fees": 50.00
      }
    ],
    "lisi": [
      {
        "id": 12347,
        "platform": "易教育",
        "username": "lisi",
        "course_name": "线性代数",
        "status": "已完成",
        "progress": "100%",
        "order_time": "2024-11-15 09:20:00",
        "fees": 60.00
      }
    ]
  }
}
```

### 3. 查询无结果
```json
{
  "code": 0,
  "msg": "未找到该账号的订单信息",
  "data": []
}
```

### 4. 请求错误
```json
{
  "code": -1,
  "msg": "请提供要查询的账号",
  "data": null
}
```

### 5. 批量查询超限
```json
{
  "code": -1,
  "msg": "批量查询最多支持100个账号，您提供了150个账号",
  "data": null
}
```

### 6. 系统错误
```json
{
  "code": -1,
  "msg": "查询失败: 数据库连接失败",
  "data": null
}
```

## 🔧 状态码说明

| HTTP状态码 | API状态码 | 说明 | 处理建议 |
|------------|-----------|------|----------|
| 200 | 1 | 查询成功 | 正常处理数据 |
| 200 | 0 | 查询成功但无数据 | 提示用户无订单信息 |
| 200 | -1 | 请求参数错误 | 检查请求参数格式 |
| 500 | -1 | 服务器内部错误 | 稍后重试或联系技术支持 |

## 🛡️ 错误处理

### 常见错误及解决方案

1. **参数缺失**
   - 错误：`请提供要查询的账号`
   - 解决：检查请求参数是否正确传递

2. **批量查询超限**
   - 错误：`批量查询最多支持100个账号`
   - 解决：减少查询账号数量或分批查询

3. **数据库连接失败**
   - 错误：`查询失败: 数据库连接失败`
   - 解决：稍后重试或联系技术支持

4. **JSON格式错误**
   - 错误：请求无响应或格式错误
   - 解决：检查JSON格式是否正确

## 💻 代码示例

### PHP示例
```php
<?php
// 单个账号查询
function queryOrder($username) {
    $url = 'https://您的域名/api/check_order.php';
    $data = json_encode(['username' => $username]);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data)
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    }
    
    return null;
}

// 批量账号查询
function queryOrdersBatch($usernames) {
    $url = 'https://您的域名/api/check_order.php';
    $data = json_encode(['usernames' => $usernames]);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($data)
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        return json_decode($response, true);
    }
    
    return null;
}

// 使用示例
$result = queryOrder('zhangsan');
if ($result && $result['code'] === 1) {
    echo "查询成功，找到 " . $result['total'] . " 条订单\n";
    foreach ($result['data'] as $order) {
        echo "订单ID: " . $order['id'] . ", 状态: " . $order['status'] . "\n";
    }
} else {
    echo "查询失败: " . ($result['msg'] ?? '未知错误') . "\n";
}
?>
```

### Python示例
```python
import requests
import json

class OrderQueryAPI:
    def __init__(self, base_url):
        self.base_url = base_url
        self.timeout = 30

    def query_order(self, username):
        """单个账号查询"""
        url = f"{self.base_url}/api/check_order.php"
        data = {"username": username}

        try:
            response = requests.post(
                url,
                json=data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None

    def query_orders_batch(self, usernames):
        """批量账号查询"""
        url = f"{self.base_url}/api/check_order.php"
        data = {"usernames": usernames}

        try:
            response = requests.post(
                url,
                json=data,
                timeout=self.timeout,
                headers={'Content-Type': 'application/json'}
            )
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None

# 使用示例
api = OrderQueryAPI("https://您的域名")

# 单个查询
result = api.query_order("zhangsan")
if result and result['code'] == 1:
    print(f"查询成功，找到 {result['total']} 条订单")
    for order in result['data']:
        print(f"订单ID: {order['id']}, 状态: {order['status']}")
else:
    print(f"查询失败: {result['msg'] if result else '未知错误'}")

# 批量查询
batch_result = api.query_orders_batch(["zhangsan", "lisi", "wangwu"])
if batch_result and batch_result['code'] == 1:
    print(f"批量查询成功，找到 {batch_result['user_count']} 个用户的 {batch_result['total']} 条订单")
    for username, orders in batch_result['grouped_data'].items():
        print(f"用户 {username}: {len(orders)} 条订单")
```

### JavaScript示例
```javascript
class OrderQueryAPI {
    constructor(baseUrl) {
        this.baseUrl = baseUrl;
        this.timeout = 30000;
    }

    async queryOrder(username) {
        const url = `${this.baseUrl}/api/check_order.php`;
        const data = { username: username };

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
                signal: AbortSignal.timeout(this.timeout)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('请求失败:', error);
            return null;
        }
    }

    async queryOrdersBatch(usernames) {
        const url = `${this.baseUrl}/api/check_order.php`;
        const data = { usernames: usernames };

        try {
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(data),
                signal: AbortSignal.timeout(this.timeout)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return await response.json();
        } catch (error) {
            console.error('请求失败:', error);
            return null;
        }
    }
}

// 使用示例
const api = new OrderQueryAPI("https://您的域名");

// 单个查询
api.queryOrder("zhangsan").then(result => {
    if (result && result.code === 1) {
        console.log(`查询成功，找到 ${result.total} 条订单`);
        result.data.forEach(order => {
            console.log(`订单ID: ${order.id}, 状态: ${order.status}`);
        });
    } else {
        console.log(`查询失败: ${result ? result.msg : '未知错误'}`);
    }
});

// 批量查询
api.queryOrdersBatch(["zhangsan", "lisi", "wangwu"]).then(result => {
    if (result && result.code === 1) {
        console.log(`批量查询成功，找到 ${result.user_count} 个用户的 ${result.total} 条订单`);
        Object.entries(result.grouped_data).forEach(([username, orders]) => {
            console.log(`用户 ${username}: ${orders.length} 条订单`);
        });
    }
});
```

### Java示例
```java
import java.io.*;
import java.net.http.*;
import java.time.Duration;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;

public class OrderQueryAPI {
    private final String baseUrl;
    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;

    public OrderQueryAPI(String baseUrl) {
        this.baseUrl = baseUrl;
        this.httpClient = HttpClient.newBuilder()
            .connectTimeout(Duration.ofSeconds(30))
            .build();
        this.objectMapper = new ObjectMapper();
    }

    public JsonNode queryOrder(String username) {
        try {
            String url = baseUrl + "/api/check_order.php";
            String jsonData = objectMapper.writeValueAsString(
                Map.of("username", username)
            );

            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(jsonData))
                .timeout(Duration.ofSeconds(30))
                .build();

            HttpResponse<String> response = httpClient.send(
                request,
                HttpResponse.BodyHandlers.ofString()
            );

            if (response.statusCode() == 200) {
                return objectMapper.readTree(response.body());
            }

        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
        }

        return null;
    }

    public JsonNode queryOrdersBatch(String[] usernames) {
        try {
            String url = baseUrl + "/api/check_order.php";
            String jsonData = objectMapper.writeValueAsString(
                Map.of("usernames", usernames)
            );

            HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(jsonData))
                .timeout(Duration.ofSeconds(30))
                .build();

            HttpResponse<String> response = httpClient.send(
                request,
                HttpResponse.BodyHandlers.ofString()
            );

            if (response.statusCode() == 200) {
                return objectMapper.readTree(response.body());
            }

        } catch (Exception e) {
            System.err.println("请求失败: " + e.getMessage());
        }

        return null;
    }

    // 使用示例
    public static void main(String[] args) {
        OrderQueryAPI api = new OrderQueryAPI("https://您的域名");

        // 单个查询
        JsonNode result = api.queryOrder("zhangsan");
        if (result != null && result.get("code").asInt() == 1) {
            System.out.println("查询成功，找到 " + result.get("total").asInt() + " 条订单");
            result.get("data").forEach(order -> {
                System.out.println("订单ID: " + order.get("id").asInt() +
                                 ", 状态: " + order.get("status").asText());
            });
        }

        // 批量查询
        String[] usernames = {"zhangsan", "lisi", "wangwu"};
        JsonNode batchResult = api.queryOrdersBatch(usernames);
        if (batchResult != null && batchResult.get("code").asInt() == 1) {
            System.out.println("批量查询成功，找到 " +
                             batchResult.get("user_count").asInt() + " 个用户的 " +
                             batchResult.get("total").asInt() + " 条订单");
        }
    }
}
```

## 🔄 高级功能

### 1. 分页查询
当订单数量较多时，建议实现分页查询：

```bash
# 获取第一页数据（每页20条）
curl "https://您的域名/api/check_order.php?username=zhangsan&page=1&limit=20"
```

### 2. 状态筛选
根据订单状态筛选：

```bash
# 只查询进行中的订单
curl -X POST "https://您的域名/api/check_order.php" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "zhangsan",
    "status": "进行中"
  }'
```

### 3. 时间范围查询
按时间范围查询订单：

```bash
# 查询指定时间范围的订单
curl -X POST "https://您的域名/api/check_order.php" \
  -H "Content-Type: application/json" \
  -d '{
    "username": "zhangsan",
    "start_time": "2025-01-01 00:00:00",
    "end_time": "2025-01-31 23:59:59"
  }'
```

## 🚀 性能优化建议

### 1. 请求优化
- 使用连接池复用HTTP连接
- 设置合理的超时时间（建议30秒）
- 批量查询时控制账号数量（建议不超过50个）

### 2. 缓存策略
- 对查询结果进行适当缓存（建议5-10分钟）
- 使用Redis等缓存系统提高响应速度

### 3. 错误重试
- 实现指数退避重试机制
- 最大重试次数建议3次
- 记录失败请求日志便于排查

## 🔒 安全建议

### 1. 请求安全
- 使用HTTPS协议传输数据
- 验证SSL证书有效性
- 避免在URL中传递敏感信息

### 2. 数据验证
- 验证返回数据的完整性
- 检查响应状态码和错误信息
- 对敏感数据进行脱敏处理

### 3. 访问控制
- 实现IP白名单机制
- 添加API访问频率限制
- 记录访问日志便于审计

## 📞 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **技术支持QQ**: 123456789
- **工作时间**: 周一至周五 9:00-18:00

### 常见问题
1. **Q: API有访问频率限制吗？**
   A: 目前没有严格限制，但建议控制在每秒10次以内。

2. **Q: 批量查询最多支持多少个账号？**
   A: API接口最多支持100个账号，网页版最多50个。

3. **Q: 如何获取更多订单字段？**
   A: 可以联系技术支持定制返回字段。

4. **Q: 是否支持Webhook推送？**
   A: 目前暂不支持，后续版本会考虑添加。

---

**版本信息**: v1.0
**更新时间**: 2025-01-01
**文档维护**: 技术团队
